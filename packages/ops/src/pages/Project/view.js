import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import {
  // SimpleShowLayout,
  TextField,
  Show,
  FunctionField,
  DateField,
  useGetOne,
  Title,
  Button,
  useUpdate,
  useNotify,
  useRefresh,
} from 'react-admin';
import InlineSVG from '../../sharedComponents/inline-svg';
import { useParams } from 'react-router-dom'; // Import useParams
import { Box, Grid, Typography, Stack } from '@mui/material';
import { getSubscriptionLabel } from '../../helpers/subscription';
import OtherProjectTable from './otherProjectTable';
import SnapShotTable from './snapshotTable';
import BasicBreadcrumbs from '../../sharedComponents/breadcrumb';
import Badge from '../../sharedComponents/badge';
import SubmissionList from './submissionTable';
// import { rowStyle } from '../../helpers/helper';

/**
 * A customized list component for displaying project data in the admin dashboard.
 *
 * @param {Object} props - Component properties.
 * @returns {JSX.Element} - React component.
 */
const ProjectView = () => {
  const { id } = useParams();
  const { data, isLoading } = useGetOne('projects', { id });
  const isCupidSelectedTag = get(data, 'isCupidSelect', false);
  const [isCupidSelected, setIsCupidSelected] = useState(isCupidSelectedTag);
  const [update] = useUpdate();
  const notify = useNotify();
  const refresh = useRefresh();

  useEffect(() => {
    setIsCupidSelected(isCupidSelectedTag);
  }, [isCupidSelectedTag]);

  if (isLoading) return <div>Loading...</div>;
  if (!data) return <div>No data found</div>;

  const handleAddCupidSelectedTag = async () => {
    const newValue = !isCupidSelected;
    setIsCupidSelected(newValue);

    try {
      await update('projects', {
        id,
        data: { isCupidSelect: newValue },
      });

      notify(
        `Cupid tag has been ${newValue ? 'added' : 'removed'}  successfully`,
        { type: 'success' }
      );
      await refresh();
    } catch (error) {
      console.error('Error updating Cupid tag:', error);
      notify('Error updating Cupid tag', { type: 'error' });
      setIsCupidSelected(!newValue);
    }
  };

  return (
    <>
      <Title title={`Project: ${data.title}`} />
      <div className="py-4">
        <BasicBreadcrumbs
          links={[
            { label: 'Projects', url: '/projects' },
            { label: data?.title || 'Untitled' },
          ]}
        />

        <Show title={false}>
          <Box
            sx={{ mt: 2, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{ justifyContent: 'space-between' }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Project Details
              </Typography>
              <Button
                label={
                  isCupidSelected
                    ? 'Remove Cupid Selected tag'
                    : 'Add Cupid Selected tag'
                }
                onClick={handleAddCupidSelectedTag}
                startIcon={
                  <InlineSVG
                    src={
                      isCupidSelected
                        ? '/assets/svg/unSelectTag.svg'
                        : '/assets/svg/selectTag.svg'
                    }
                  />
                }
                sx={{
                  p: 1,
                  backgroundColor: isCupidSelected ? 'white' : '#05012D',
                  color: isCupidSelected ? '#05012D' : 'white',
                  border: '1px solid #05012D',
                }}
              />
            </Stack>
            <Grid container spacing={2} mt={2}>
              {get(data, 'title', '') !== '' && (
                <Grid item xs={6} md={3}>
                  <Typography fontWeight="bold">Project</Typography>
                  <TextField source="title" />
                </Grid>
              )}
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Registration No</Typography>
                <TextField source="regNo" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Created</Typography>
                <DateField source="createdAt" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Updated</Typography>
                <DateField source="updatedAt" />
              </Grid>
            </Grid>

            <Grid container spacing={2} mt={2}>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Project Creator</Typography>
                <TextField source="projectCreator" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Producer</Typography>
                <TextField source="producer" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Director</Typography>
                <TextField source="director" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Writer</Typography>
                <TextField source="writer" />
              </Grid>
            </Grid>

            <Grid container spacing={2} mt={2}>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Format</Typography>
                <TextField source="format" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Genre</Typography>
                <TextField source="genre" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Setting</Typography>
                <TextField source="setting" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Subscription Status</Typography>
                <FunctionField
                  source="subscription"
                  sortable={false}
                  label={
                    <span style={{ fontWeight: 'bold' }}>Subscription</span>
                  }
                  render={(record) => {
                    const subsStatus = get(record, 'subscription');
                    const { badgeColor, labelText } =
                      getSubscriptionLabel(subsStatus);
                    return (
                      <Badge
                        list={[
                          {
                            label: labelText,
                            className: `badge-btn ${badgeColor} fs-12`,
                          },
                        ]}
                      />
                    );
                  }}
                />
              </Grid>
            </Grid>

            {/* <Grid container spacing={2} mt={2}>
              <Grid item xs={12}>
                <Badge
                  list={[{ label: 'Active', className: 'badge-btn fs-12' }]}
                />
              </Grid>
            </Grid> */}

            <Grid container spacing={2} mt={2}>
              <Grid item xs={12}>
                <Typography fontWeight="bold">Log Line</Typography>
                <TextField source="logLine" />
              </Grid>
            </Grid>

            <Grid container spacing={2} mt={2}>
              <Grid item xs={12}>
                <Typography fontWeight="bold">Tags</Typography>
                <FunctionField
                  label={
                    <span
                      style={{
                        fontWeight: 'bold',
                        fontSize: '14px',
                        color: 'black',
                      }}
                    >
                      Tags
                    </span>
                  }
                  render={(record) => {
                    const tags = get(record, 'projectTags', []);
                    return (
                      <Badge
                        list={(Array.isArray(tags) ? tags : []).map((text) => ({
                          label: text,
                          className: 'admin-btn tag-item',
                        }))}
                      />
                    );
                  }}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} mt={2}>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Total Funding</Typography>
                <TextField source="budget" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Budget/Unknown Budget</Typography>
                <TextField source="unestimatedBudget" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Total Budget Value</Typography>
                <TextField source="financePlan" />
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Script/Treatment</Typography>
                <FunctionField
                  source="script"
                  render={(record) =>
                    record.script ? (
                      <a
                        href={record.script}
                        className="admin-link"
                        download
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Download
                      </a>
                    ) : null
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} mt={2}>
              <Grid item xs={6} md={3}>
                <Typography fontWeight="bold">Status</Typography>
                <FunctionField
                  source="status"
                  render={(record) => (
                    <Badge
                      list={[
                        {
                          label: get(record, 'status', 'N/A'),
                          className: 'admin-btn',
                        },
                      ]}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </Show>

        <div className="mt-4">
          <SnapShotTable />
        </div>
        <div className="mt-4">
          <SubmissionList
            projectCreatorId={get(data, 'projectCreatorId', '')}
          />
        </div>
        <div className="mt-4">
          <OtherProjectTable />
        </div>
      </div>
    </>
  );
};
export default ProjectView;
