import jsonExport from 'jsonexport/dist';
import { downloadCSV } from 'react-admin';
import { startCase, get } from 'lodash';
import { organisationCount } from '../helpers/helper';

const CustomExporter = (
  list,
  key,
  selectedFields = null,
  customNames = {} // <-- new argument
) => {
  let showColumns = [];
  const rows = [];
  if (Array.isArray(list) && list.length > 0) {
    const sanitize = (value) =>
      typeof value === 'string' ? value.replace(/;/g, '') : value;
    for (const project of list) {
      rows.push({
        snapshotName: sanitize(get(project, 'snapshotName', 'NA')),
        projectName: sanitize(get(project, 'projectName', 'NA')),
        calloutName: sanitize(get(project, 'calloutName', 'NA')),
        projectCreator: get(project, 'projectCreator', ''),
        creatorEmail: get(project, 'creatorEmail', ''),
        subscription: get(project, 'subscription', ''),
        snapshotCreateAt: get(project, 'snapshotCreateAt', ''),
        addedAt: get(project, 'addedAt', ''),
        status: get(project, 'status', ''),
        snapshotLink: get(project, 'snapshotLink', ''),
      });
    }
  }
  if (selectedFields && selectedFields.length > 0) {
    showColumns = selectedFields.map((field) => ({ source: field }));
  } else {
    const localColumns = localStorage.getItem(
      `RaStore.preferences.${key}.datagrid.columns`
    );
    const localOmitData = localStorage.getItem(
      `RaStore.preferences.${key}.datagrid.omit`
    );
    const localAvailableData = localStorage.getItem(
      `RaStore.preferences.${key}.datagrid.availableColumns`
    );

    const omit = localOmitData ? JSON.parse(localOmitData) : [];
    const available = localAvailableData ? JSON.parse(localAvailableData) : [];
    const columns = localColumns ? JSON.parse(localColumns) : [];

    showColumns = available.filter((column) => !omit.includes(column.source));
    if (columns.length > 0) {
      showColumns = available.filter((column) =>
        columns.includes(column.index)
      );
    }
  }

  const listForExport = rows.map((item) => {
    const newItem = {};
    showColumns.forEach((column) => {
      // Determine the display name for this column
      const displayName =
        customNames[column.source] || startCase(column.source);

      if (column.source === 'organisationSize') {
        newItem[displayName] = organisationCount(item[column.source]);
      } else {
        newItem[displayName] = item[column.source];
      }
    });
    return newItem;
  });

  jsonExport(listForExport, {}, (err, csv) => {
    if (err) {
      console.error(err);
      return;
    }
    downloadCSV(csv, startCase(key));
  });
};

export default CustomExporter;
