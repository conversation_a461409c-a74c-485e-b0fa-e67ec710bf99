/* eslint-disable indent */

const setAPIUrl = (apiUrl, type, resource) => {
  if (
    (resource === 'users' ||
      resource === 'projects' ||
      resource === 'snapshots' ||
      resource === 'projectList' ||
      resource === 'calloutList' ||
      resource === 'snapshots') &&
    (type === 'GET_LIST' || type === 'DELETE')
  ) {
    apiUrl = process.env.REACT_APP_API_BASE_URL;
  } else if (
    (resource === 'users' || resource === 'projects') &&
    type === 'GET_ONE'
  ) {
    apiUrl = process.env.REACT_APP_API_BASE_URL;
  } else if (resource === 'updateUser' && type === 'UPDATE') {
    apiUrl = process.env.REACT_APP_API_BASE_URL;
  } else if (resource === 'execute' && type === 'CREATE') {
    apiUrl = process.env.REACT_APP_SMASH_AI_BASE_URL;
  } else {
    apiUrl = process.env.REACT_APP_API_BASE_URL;
  }

  return apiUrl;
};

const setResource = (type, resource, params) => {
  if (resource === 'users' && type === 'GET_LIST') {
    resource = 'v1/user/ops/list';
  }
  if (resource === 'users' && type === 'GET_ONE') {
    resource = `v1/user/ops`;
  } else if (resource === 'projects' && type === 'GET_LIST') {
    resource = 'v1/user/projects';
  } else if (resource === 'projectList' && type === 'GET_LIST') {
    resource = `v1/user/ops/projects`;
  } else if (resource === 'snapshots' && type === 'GET_LIST') {
    resource = `v1/project/ops/snapshot`;
  } else if (resource === 'projects' && type === 'GET_ONE') {
    resource = `v1/project/ops`;
  } else if (resource === 'projects' && type === 'UPDATE') {
    resource = `v1/project/admin/${params.id}/update`;
    params.data = { ...params.data, _id: params.id };
    delete params.id;
  } else if (resource === 'tag' && type === 'GET_LIST') {
    resource = 'v1/tag';
  } else if (resource === 'tag' && type === 'GET_ONE') {
    resource = 'v1/tag';
  } else if (resource === 'updateUser' && type === 'UPDATE') {
    resource = 'v1/user';
  } else if (resource === 'callouts' && type === 'GET_LIST') {
    resource = 'v1/callout/list';
  } else if (resource === 'calloutList' && type === 'GET_LIST') {
    resource = `v1/user/ops/callouts`;
  } else if (
    resource === 'callouts' &&
    (type === 'GET_ONE' || type === 'EDIT')
  ) {
    resource = 'v1/callout';
  } else if (resource === 'callouts' && type === 'DELETE') {
    resource = 'v1/callout';
  } else if (resource === 'calloutSlats' && type === 'GET_LIST') {
    if (params?.filter?.id) {
      resource = `v1/callout/submissions/${params.filter.id}`;
    } else {
      resource = 'v1/callout/submissions';
    }
  } else if (resource === 'calloutSubmissions' && type === 'GET_LIST') {
    if (params?.filter?.id) {
      resource = `v1/callout/submissions/${params.filter.id}`;
    } else {
      resource = 'v1/callout/submissions';
    }
  } else if (resource === 'userSubmission' && type === 'GET_LIST') {
    resource = 'userSubmission';
  } else if (resource === 'module' && type === 'GET_LIST') {
    resource = 'v1/marketplace/module';
  } else if (resource === 'moduleSnapshots' && type === 'GET_LIST') {
    resource = 'v1/marketplace/module/snapshots';
  } else if (resource === 'submissionList' && type === 'GET_LIST') {
    resource = 'v1/submission';
  } else if (resource === 'otherProjectList' && type === 'GET_LIST') {
    resource = 'v1/user/ops/projects';
  } else if (resource === 'submissionType' && type === 'UPDATE') {
    resource = `v1/submission/${params.id}/type`;
    // Remove id from params so it doesn't get appended again by the dataProvider
    if (params && params.id) delete params.id;
  } else if (resource === 'submissionFeedback' && type === 'CREATE') {
    resource = `v1/submission/${params.id}/feedback`;
    if (params && params.id) delete params.id;
  } else if (resource === 'submissionFeedback' && type === 'UPDATE') {
  } else if (resource === 'submissionStatus' && type === 'UPDATE') {
    resource = `v1/submission/${params.id}/tracking`;
    if (params && params.id) delete params.id;
  }
  return resource;
};

const setParams = (type, resource, params) => {
  // modified param here if needed
  // Project updates are handled in setResource, so we don't need any special handling here for projects
  if (type === 'GET_ONE' && resource === 'v1/tag') {
    params._id = params.id;
  }
  if (type === 'GET_ONE' && resource === 'v1/user/ops') {
    params._id = params.id;
  }
  if (type === 'GET_LIST' && resource === 'v1/user/ops/projects') {
    params._id = params.filter.id;
    delete params.filter.id;
  }

  if (type === 'GET_LIST' && resource === 'v1/user/ops/callouts') {
    params._id = params.filter.id;
    delete params.filter.id;
  }

  if (type === 'GET_LIST' && resource === 'v1/marketplace/module') {
    params._id = params.filter.id;
    delete params.filter.id;
  }
  if (type === 'GET_LIST' && resource === 'v1/project/ops/snapshot') {
    params._id = params.filter.id;
    delete params.filter.id;
  }
  if (type === 'GET_LIST' && resource === 'v1/callout/list') {
    params.filter = { ...params.filter };
  }
  if (type === 'GET_LIST' && resource === 'v1/marketplace/module/snapshots') {
    params._id = params.filter.id;
    delete params.filter.id;
  }
  if (type === 'GET_ONE' && resource === 'v1/callout') {
    // additional logic if needed
  }
  if (
    (type === 'UPDATE' || type === 'CREATE') &&
    (resource === 'v1/user/ops/list' || resource === 'v1/user/projects')
  ) {
    // additional logic if needed
  }
  if (type === 'GET_LIST' && resource.startsWith('v1/callout/submissions')) {
    return {
      ...params,
      filter: {
        ...params.filter,
      },
      meta: {
        ...params.meta,
        select: params.filter.select,
        calloutId: params.filter.calloutId,
      },
    };
  }

  if (type === 'GET_LIST' && resource === 'v1/user/projects') {
    return {
      ...params,
      filter: {
        ...params.filter,
      },
      meta: {
        ...params.meta,
        populate: 'lastSnapshot',
      },
    };
  }

  if (type === 'GET_LIST' && resource === 'v1/submission') {
    return {
      ...params,
      meta: {
        ...(params.meta || {}),
        select:
          'project.cover.title, callout.name ,snapshot.name,snapshot.body,snapshot.hash,project.creator.metadata.subscriptionType,snapshot.createdAt',
      },
    };
  }

  if (type === 'GET_ONE' && resource === 'v1/user/ops/list') {
    // additional logic if needed
  }
  if (type === 'DELETE' && resource === 'v1/callout') {
    params._id = params.id;
  }
  return params;
};

module.exports = {
  setResource,
  setParams,
  setAPIUrl,
};
