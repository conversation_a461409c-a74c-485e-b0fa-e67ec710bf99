/* eslint-disable indent */
import { get } from 'lodash';
import moment from 'moment';
import { formatDate } from '../helpers/helper';

const modifiedResponse = (response, type, resource) => {
  const userData = get(response, 'json.data.userList.docs');
  const projectData = get(response, 'json.data.docs');
  const projectList = get(response, 'json.data.docs');
  const calloutList = get(response, 'json.data.docs');
  const snapshotView = get(response, 'json.data.docs', []);
  let newResp = response;
  if (resource === 'users' && type === 'GET_LIST') {
    newResp = userData.map((user) => ({
      id: get(user, '_id', ''),
      userName: get(user, 'profile.name.fullName', 'undefined'),
      created: moment(get(user, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
      organisationName: get(user, 'profile.organisation', ''),
      organisationType: get(user, 'profile.organisationType', ''),
      organisationSize: get(user, 'profile.organisationSize', ''),
      projectNotifications: get(user, 'userMeta.acceptProjectNotifications'),
      termsPrivacy: get(user, 'userMeta.agreeTermsPrivacy'),
      newsAndUpdates: get(user, 'userMeta.receiveMarketMaterial'),
      receiveMarketMaterial: get(user, 'userMeta.receiveMarketMaterial'),
      organisationLogo: get(user, 'profile.organisationLogo'),
      email: get(user, 'email', ''),
      organisationBio: get(user, 'profile.discovererProfile', ''),
      city: get(user, 'profile.city.address', ''),
      location: get(user, 'profile.city', ''),
      subscription: get(user, 'userMeta.type'),
    }));
  } else if (resource === 'projects' && type === 'GET_LIST') {
    newResp = projectData.map((project) => {
      const tags = get(project, 'basicInfo.tags', []).map((tag) => tag.text);
      return {
        id: get(project, '_id', ''),
        projectName: get(project, 'cover.title', 'undefined'),
        creatorName: get(project, 'creator.username', 'undefined'),
        creatorEmail: get(project, 'creator.email', ''),
        registrationNo: get(project, 'regNo', ''),
        projectTags:
          tags.length > 0
            ? `${tags[0]}${tags.length > 1 ? ` + ${tags.length - 1} more` : ''}`
            : '',
        created: moment(get(project, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        updated: moment(get(project, 'updatedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        totalSnapshot: get(project, 'totalSnapshot.all', 0),
        status: get(project, 'basicInfo.status', ''),
        producer: get(project, 'cover.producer', ''),
        director: get(project, 'cover.director', ''),
        writer: get(project, 'cover.writer', ''),
        format: get(project, 'basicInfo.format', ''),
        genre: get(project, 'basicInfo.genre', ''),
        setting: get(project, 'basicInfo.setting', ''),
        totalBudget: get(project, 'totalBudget', ''),
        totalFunding: get(project, 'totalFinanceSum', ''),
        subscription: get(project, 'creator.metadata.subscriptionType', ''),
        treatment: get(project, 'projectDisc.treatment.url', ''),
        script: get(project, 'projectDisc.script.url', ''),
        logLine: get(project, 'basicInfo.logLine', ''),
        projectSnapshot: get(project, 'lastSnapshot.hash', ''),
        unestimatedBudget: get(project, 'unestimatedBudget.amount'),
        projectCreatorId: get(project, 'creator.userId'),
        showStatus: get(project, 'unestimatedBudget.showStatus', ''),
      };
    });
  } else if (resource === 'users' && type === 'GET_ONE') {
    const userView = get(response, 'json.data.user', {});
    newResp = {
      data: {
        id: get(userView, '_id', ''),
        firstName: get(userView, 'profile.name.firstName', ''),
        lastName: get(userView, 'profile.name.lastName', ''),
        userName: get(userView, 'profile.name.fullName', 'undefined'),
        created: moment(get(userView, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        email: get(userView, 'email', ''),
        subscriptionStatus: get(userView, 'userMeta.type', ''),
        organisationName: get(userView, 'profile.organisation', ''),
        organisationType: get(userView, 'profile.organisationType', ''),
        organisationBio: get(userView, 'profile.discovererProfile', ''),
        organisationLogo: get(userView, 'profile.organisationLogo', null),
        occupationType: get(userView, 'profile.occupationType', ''),
        profileImage: get(userView, 'profile.profileImage', ''),
        location: get(userView, 'profile.city.address', ''),

        projectNotifications:
          get(userView, 'userMeta.acceptProjectNotifications', false) === true
            ? 'True'
            : 'False',

        termsPrivacy:
          get(userView, 'userMeta.agreeTermsPrivacy', false) === true
            ? 'True'
            : 'False',

        receiveMarketMaterial:
          get(userView, 'userMeta.receiveMarketMaterial', false) === true
            ? 'True'
            : 'False',
      },
    };
  } else if (resource === 'users' && type === 'GET_MANY') {
    const userView = get(response, 'json.data.user', {});
    newResp = {
      data: [
        {
          id: get(userView, '_id', ''),
          firstName: get(userView, 'profile.name.firstName', ''),
          lastName: get(userView, 'profile.name.lastName', ''),
          userName: get(userView, 'profile.name.fullName', 'undefined'),
          created: moment(get(userView, 'createdAt')).format(
            'YYYY-MM-DD HH:mm:ss'
          ),
          email: get(userView, 'email', ''),
          subscriptionStatus: get(userView, 'userMeta.type', ''),
          organisationName: get(userView, 'profile.organisation', ''),
          organisationType: get(userView, 'profile.organisationType', ''),
          organisationBio: get(userView, 'profile.discovererProfile', ''),
          organisationLogo: get(userView, 'profile.organisationLogo'),
          occupationType: get(userView, 'profile.occupationType', ''),
          profileImage: get(userView, 'profile.profileImage', ''),
          location: get(userView, 'profile.city.address', ''),

          projectNotifications:
            get(userView, 'userMeta.acceptProjectNotifications', false) === true
              ? 'True'
              : 'False',

          termsPrivacy:
            get(userView, 'userMeta.agreeTermsPrivacy', false) === true
              ? 'True'
              : 'False',

          receiveMarketMaterial:
            get(userView, 'userMeta.receiveMarketMaterial', false) === true
              ? 'True'
              : 'False',
        },
      ],
    };
  } else if (resource === 'projectList' && type === 'GET_LIST') {
    newResp = projectList.map((project) => {
      const tags = get(project, 'basicInfo.tags', []).map((tag) => tag.text);
      return {
        id: get(project, '_id', ''),
        title: get(project, 'cover.title', '').trim() || 'UNTITLED',
        projectCreator:
          get(project, 'creator.username', '').trim() || 'UNTITLED',
        projectCreatorEmail: get(project, 'creator.email', ''),
        regNo: get(project, 'regNo', ''),
        projectTags:
          tags.length > 0
            ? `${tags[0]}${tags.length > 1 ? ` + ${tags.length - 1} more` : ''}`
            : '',
        createdAt: moment(get(project, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        updatedAt: moment(get(project, 'updatedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        status: get(project, 'basicInfo.status'),
      };
    });
  } else if (resource === 'otherProjectList' && type === 'GET_LIST') {
    newResp = projectList.map((project) => {
      const tags = get(project, 'basicInfo.tags', []).map((tag) => tag.text);
      return {
        id: get(project, '_id', ''),
        title: get(project, 'cover.title', '').trim() || 'UNTITLED',
        projectCreator:
          get(project, 'creator.username', '').trim() || 'UNTITLED',
        projectCreatorEmail: get(project, 'creator.email', ''),
        regNo: get(project, 'regNo', ''),
        projectTags:
          tags.length > 0
            ? `${tags[0]}${tags.length > 1 ? ` + ${tags.length - 1} more` : ''}`
            : '',
        createdAt: moment(get(project, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        updatedAt: moment(get(project, 'updatedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        status: get(project, 'basicInfo.status'),
      };
    });
  } else if (resource === 'calloutList' && type === 'GET_LIST') {
    newResp = calloutList.map((callout) => {
      return {
        id: get(callout, '_id', ''),
        title: get(callout, 'name', '').trim() || 'UNTITLED',
        projectCreator:
          get(callout, 'discoverer.name', '').trim() || 'UNTITLED',
        organisation: get(callout, 'body.companyName', ''),
        genre: get(callout, 'body.genre', ''),
        created: moment(get(callout, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        updatedAt: moment(get(callout, 'updatedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        submissions: get(callout, 'submissions', '').length,
        slates: get(callout, 'slates', '').length,
        status: get(callout, 'status', ''),
      };
    });
  } else if (resource === 'calloutList' && type === 'DELETE') {
    const extractedData = get(response, 'json.data', []).map((doc) => ({
      id: get(doc, '_id'),
    }));
    newResp = { data: extractedData };
  } else if (resource === 'projects' && type === 'GET_ONE') {
    const projectView = get(response, 'json.data.project', {});
    const tags = get(projectView, 'basicInfo.tags', []);
    const projectTags = Array.isArray(tags)
      ? tags.map((tag) => tag?.text || '')
      : [];

    newResp = {
      data: {
        projectCreatorId: get(projectView, 'creator.userId', ''),
        id: get(projectView, '_id', ''),
        title: get(projectView, 'cover.title', ''),
        regNo: get(projectView, 'regNo', ''),
        createdAt: moment(get(projectView, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        updatedAt: moment(get(projectView, 'updatedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        projectCreator: get(projectView, 'creator.username', ''),
        producer: get(projectView, 'cover.producer', ''),
        director: get(projectView, 'cover.director', ''),
        writer: get(projectView, 'cover.writer', ''),
        format: get(projectView, 'basicInfo.format', ''),
        genre: get(projectView, 'basicInfo.genre', ''),
        setting: get(projectView, 'basicInfo.setting', ''),
        logLine: get(projectView, 'basicInfo.logLine', ''),
        budget: get(projectView, 'totalBudget', 0),
        unestimatedBudget: get(projectView, 'unestimatedBudget.amount', 0),
        financePlan: get(projectView, 'totalFinanceSum', 0),
        script: get(projectView, 'projectDisc.script.url', ''),
        status: get(projectView, 'basicInfo.status', ''),
        projectTags: projectTags,
        subscription: get(projectView, 'creator.metadata.subscriptionType', ''),
        isCupidSelect: get(projectView, 'isCupidSelect', false),
      },
    };
  } else if (resource === 'snapshots' && type === 'GET_LIST') {
    newResp = snapshotView.map((snapshot) => {
      return {
        id: get(snapshot, '_id', ''),
        snapshotTitle: (get(snapshot, 'notes') ?? '').trim() || 'UNTITLED',
        created: moment(get(snapshot, 'createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        updated: moment(get(snapshot, 'updatedAt', '')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        snapShotStatus:
          get(snapshot, 'publish') === true ? 'Published' : 'Draft',
        hash: get(snapshot, 'hash', ''),
      };
    });
  } else if (resource === 'submissionList' && type === 'GET_LIST') {
    const docs = get(response, 'json.data.docs', []);
    return docs.map((sub) => {
      const snapshotBody = get(sub, 'snapshot.body', '');
      const snaps = JSON.parse(snapshotBody || '{}');

      return {
        id: get(sub, '_id', ''),
        callOutName: get(sub, 'calloutName', ''),
        snapProjectName: get(sub, 'projectName', ''),
        snapshotCreateAt: moment(get(sub, 'snapshot.createdAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        creatorEmail: get(sub, 'projectCreator.email', ''),
        submissionsStatus: get(sub, 'status', ''),
        hash: get(sub, 'snapshot.hash', ''),
        snapshotLink: (() => {
          const snapHash = get(sub, 'snapshot.hash', '');
          if (!snapHash) return '';
          const baseUrl = process.env.REACT_APP_WEBAPP_URL || '';
          return `${baseUrl}/project/snap/${snapHash}?status=${sub.submissionsStatus}&id=${sub.id}`;
        })(),
        projectId: get(sub, 'project._id', ''),
        calloutId: get(sub, 'callout._id', ''),
        submittedBy: get(sub, 'submittedBy.username', ''),
        snapshot: get(sub, 'snapshot', ''),
        projectName: get(sub, 'project.cover.title', ''),
        calloutName: get(sub, 'callout.name', ''),
        addedAt: moment(get(sub, 'addedAt')).format('YYYY-MM-DD HH:mm:ss'),
        status: get(sub, 'status', ''),
        isEmailSent: get(sub, 'isEmailSent', false),
        type: get(sub, 'type', ''),
        feedback: get(sub, 'feedback', []),
        slateUpdatedAt: moment(get(sub, 'slateUpdatedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        slateUpdatedBy: get(sub, 'slateUpdatedBy.username', ''),
        projectCreator: get(sub, 'projectCreator.username', ''),
        snapshotCreator: get(sub, 'snapshotCreator.username', ''),
        snapshotName: get(snaps, 'cover.title', 'Untitled'),
        snapshotHash: get(sub, 'snapshot.hash', ''),
        projectCreatorId: get(sub, 'projectCreator.userId', ''),
        submittyedByRole: get(sub, 'submittedBy.role', ''),
        genre: get(snaps, 'basicInfo.genre', ''),
        note: get(sub, 'note', ''),
        subscription: get(sub, 'project.creator.metadata.subscriptionType', ''),
        viewed: get(sub, 'activity.action', false) === 'view',
      };
    });
  }

  if (resource === 'v1/callout/submissions/project' && type === 'GET_LIST') {
    const projectSubmissionList = get(response, 'json.data.docs', []);
    newResp = projectSubmissionList.map((submission) => {
      return {
        id: get(submission, '_id', ''),
        snapshot: get(submission, 'snapshot', {}),
        calloutName: get(submission, 'calloutName', '').trim() || 'UNTITLED',
        calloutId: get(submission, 'calloutId', ''),
        creator: get(submission, 'creator', {}),
        addedAt: moment(get(submission, 'addedAt')).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        status: get(submission, 'status', 'NEW'),
        feedback: get(submission, 'feedback', ''),
        type: get(submission, 'type', 'submission'),
        notes: get(submission, 'notes', ''),
      };
    });
  }
  if (resource === 'tag' && type === 'GET_LIST') {
    newResp = get(response, 'json.data.docs').map((user) => ({
      id: get(user, '_id', ''),
      tags: get(user, 'text', ''),
    }));
  }

  if (resource === 'callouts' && type === 'GET_LIST') {
    const calloutData = get(response, 'json.data.docs');
    newResp = calloutData
      .filter(
        (item) =>
          get(item, 'userMeta.type') !== null &&
          get(item, 'discoverer.id') !== null
      )
      .map((callout) => ({
        id: get(callout, '_id', ''),
        name: get(callout, 'name', '') || 'Untitled',
        companyName: get(callout, 'body.companyName', 'undefined'),
        discoverer: get(callout, 'discoverer.name', ''),
        createdAt: formatDate(get(callout, 'createdAt', null)),
        totalSubmissions: get(callout, 'totalSubmissions', ''),
        newsubmissionsCount: get(callout, 'newsubmissionsCount', ''),
        totalSlates: get(callout, 'totalSlates', ''),
        status: get(callout, 'status', ''),
        subscription: get(callout, 'userMeta.type'),
        discovererId: get(callout, 'discoverer.id', ''),
        isPublished: get(callout, 'isPublished'),
      }));
  }

  if (resource === 'moduleSnapshots' && type === 'GET_LIST') {
    const moduleSnapshot = get(response, 'json.data.snapshots');
    newResp = moduleSnapshot;
  }

  if (resource === 'callouts' && type === 'GET_ONE') {
    const callout = get(response, 'json.data');
    newResp = {
      data: {
        genres: get(callout, 'genres', null),
        opportunities: get(callout, 'opportunities', null),
        body: get(callout, 'body', ''),
        id: get(callout, '_id', ''),
        name: get(callout, 'name', ''),
        companyName: get(callout, 'body.companyName', 'undefined'),
        discoverer: get(callout, 'discoverer.name', ''),
        email: get(callout, 'discoverer.email', ''),
        submissions: get(callout, 'submissions', ''),
        slats: get(callout, 'slates', ''),
        status: get(callout, 'status', ''),
        subscription: get(callout, 'userMeta.type'),
        discovererId: get(callout, 'discoverer.id', ''),
        enterPrisePlanId: get(callout, 'enterPrisePlanId', ''),
        enterPrisePriceId: get(callout, 'enterPrisePriceId', ''),
        isPublished: get(callout, 'isPublished', false),
      },
    };
  }
  if (resource === 'calloutSubmissions' && type === 'GET_LIST') {
    const submissions = get(response, 'json.data.docs');

    newResp = submissions.map((submission) => {
      const userSnapshot = get(submission, 'submissions.snapshot.body', '{}');
      const snapshotBody = JSON.parse(userSnapshot || '{}');
      return {
        id: get(submission, 'submissions._id', ''),
        hash: get(submission, 'submissions.snapshot.hash', ''),
        title: get(submission, 'submissions.snapshot.title', '') || 'Untitled',
        projectId: get(snapshotBody, '_id', ''),
        userId: get(snapshotBody, 'creator.userId', ''),
        creatorName:
          get(snapshotBody, 'contactDetails.fullName', '') || 'Untitled',
        creatorEmail: get(snapshotBody, 'creator.email', '') || 'Untitled',
        addedAt: formatDate(get(submission, 'submissions.addedAt', '')),
        submissionAddedAt: formatDate(
          get(submission, 'submissions.submissionAddedAt', '')
        ),
        status:
          get(submission, 'submissions.status', '') === 'NEW' &&
          get(submission, 'submissions.creator.submittedBy', '') === 'cupid'
            ? 'cupid_selected'
            : get(submission, 'submissions.status', ''),
        isEmailSent: get(submission, 'submissions.isEmailSent', ''),
        notes: get(submission, 'submissions.notes', ''),
        projectName: get(snapshotBody, 'cover.title', ''),
        // genre: get(snapshotBody, 'basicInfo.genre', ''),
        feedback: get(submission, 'submissions.feedback', ''),
      };
    });
  }
  if (resource === 'calloutSlats' && type === 'GET_LIST') {
    const slates = get(response, 'json.data.docs');
    newResp = slates.map((slate) => {
      const userSnapshot = get(slate, 'slates.snapshot.body', '{}');
      const snapshotBody = JSON.parse(userSnapshot || '{}');

      return {
        id: get(slate, 'slates._id', ''),
        hash: get(slate, 'slates.snapshot.hash', ''),
        title: get(slate, 'slates.snapshot.title', '') || 'Untitled',
        projectId: get(snapshotBody, '_id', ''),
        userId: get(snapshotBody, 'creator.userId', ''),
        creatorName:
          get(snapshotBody, 'contactDetails.fullName', '') || 'Untitled',
        creatorEmail: get(snapshotBody, 'creator.email', '') || 'Untitled',
        addedAt: formatDate(get(slate, 'slates.addedAt', '')),
        submissionAddedAt: formatDate(
          get(slate, 'slates.submissionAddedAt', '')
        ),
        status: get(slate, 'slates.status', ''),
        isEmailSent: get(slate, 'slates.isEmailSent', ''),
        notes: get(slate, 'slates.notes', ''),
        projectName: get(snapshotBody, 'cover.title', ''),
        // genre: get(snapshotBody, 'basicInfo.genre', ''),
        feedback: get(slate, 'slates.feedback', ''),
      };
    });
  }
  if (resource === 'module' && type === 'GET_LIST') {
    newResp = get(response, 'json.data');
  }
  return newResp;
};

export default modifiedResponse;
