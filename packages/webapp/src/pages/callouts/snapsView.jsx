/* eslint-disable no-useless-escape */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import HoverToolTip from 'sharedComponents/ToolTip/hoverToolTip';
import Style from 'pages/project/share/style/share.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import mixpanelAnalytics from 'lib/mixpanel';

const SnapsView = (props) => {
  const {
    snapsList,
    getProjectSnapshots,
    projectPreviewData,
    setSelectedSnapId,
    selectedSnapId,
    setSelectedSnapBody,
    submissions,
  } = props;

  useEffect(() => {
    const fetchHistory = async () => {
      if (get(projectPreviewData, '_id')) {
        await getProjectSnapshots({
          projectId: projectPreviewData._id,
        });
      }
    };
    fetchHistory();
  }, [getProjectSnapshots, projectPreviewData, submissions]);

  const handleSnapSelect = (item) => {
    setSelectedSnapId(item._id);
    setSelectedSnapBody(item);
  };

  const snapList =
    snapsList &&
    snapsList.map((item) => {
      const date = item.createdAt.split('T')[0];
      const time = item.createdAt.split('T')[1];
      const newTime = time.split('.')[0];

      const isDisabled = submissions.some(
        (submission) => submission.snapshot && submission.snapshot.id === item._id
        // Optional chaining alternative:
        // (submission) => submission.snapshot?.id === item._id
      );

      return (
        <div
          className={`${Style.listContainer} col-12 d-flex flex-row mb-2  ${
            selectedSnapId === item._id ? Style.selectedList : ''
          } ${isDisabled && Style.disabledList} `}
          key={item._id}
          onClick={() => !isDisabled && handleSnapSelect(item)}
        >
          <div className={`d-flex w-100 p-0 ${isDisabled && 'ml-24'}`}>
            <div className={`mr-24 align-self-center ${Style.radioButton}`}>
              {!isDisabled && (
                <input
                  type="radio"
                  checked={selectedSnapId === item._id}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleSnapSelect(item);
                    mixpanelAnalytics('select_snapshot', {
                      pageName: 'callouts_projects_list',
                      snapshotName: get(item, 'notes', 'unknown title'),
                    });
                  }}
                  disabled={isDisabled}
                />
              )}
            </div>
            <div className="col-9 col-md-5 col-lg-5 p-0 align-self-center">
              <p className="m-0">{item.notes || 'Unknown title'}</p>
              <p className="m-0 d-block d-sm-none mt-2">
                {date} {newTime}
              </p>
            </div>
            <div className="col-5 align-self-center d-none d-sm-block">
              <p className="m-0">
                {date} {newTime}
              </p>
            </div>
            <div className={`d-flex ml-auto justify-content-around mb-2 `}>
              <div
                className="text-center align-self-center"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(`/project/snap/${item.hash}`, '_blank');
                }}
              >
                <HoverToolTip
                  clickIcon={
                    <InlineSvg
                      height={24}
                      width={24}
                      src="/assets/svg/publishView.svg"
                    />
                  }
                  position="top"
                  contentClass="p2 text-left"
                  content="View Snapshot"
                />
              </div>
            </div>
          </div>
        </div>
      );
    });

  return (
    <div className="col-12 mt-3 mb-mt-4 mb-lg-4 p-0">
      <div className="row m-0 mb-35">{snapList}</div>
    </div>
  );
};

SnapsView.propTypes = {
  projectPreviewData: PropTypes.object.isRequired,
  handleChangePublishSnap: PropTypes.func.isRequired,
  snapsList: PropTypes.array.isRequired,
  getHistory: PropTypes.func.isRequired,
  initialize: PropTypes.func.isRequired,
};

export default SnapsView;
