@import '../../../../styles/variable.scss';
@import '../../../../styles/mixins.scss';

.containerBg {
  background-color: rgba(216, 216, 216, 0.6);
  z-index: 10;
  position: absolute;
  padding-bottom: 500px;
  width: 100% !important;
}

.getStartShareContainer {
  box-sizing: border-box;
  max-width: 100%;
  border: 2px solid $black;
  background-color: $white;
}

.leftSide {
  position: fixed;
  background-color: #f6f6f6;
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 0.2);
  height: 100vh !important;
}

.rightSide {
  // position: relative;
  // top: 50;
  padding-top: 36px;
}

.shareContainer {
  margin: 0 auto;
}

.shareRowBox {
  margin: 0 auto;
  width: 90%;
}

.shareMainContainer {
  padding-bottom: 100px;
}

.stepContainer {
  width: 100%;
  padding-left: 72px;
  padding-right: 72px;
  min-height: 120px;
  border-bottom: 2px solid $black;
}

.gettingReadyInActive {
  color: $gettingReady;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.2px;
  line-height: 24px;
  margin-left: 12px;
}

.hrLine {
  width: 100%;
  border-bottom: 1px solid $black;
  @media (max-width: $breakpoint-sm) {
    width: 17%;
  }
}

.stepNumber {
  border-radius: 50%;
  width: 38px;
  height: 38px;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.2px;
  line-height: 24px;
  text-align: center;
  color: $white;
  padding-top: 8px;
}

.stepBgActive {
  background-color: $navy;
}

.stepBgInActive {
  background-color: $gettingReady;
}

/*********************************************************
* Add Note
**********************************************************/

.addNoteTitle {
  margin-top: 50px;
  color: $black;
  font-size: 20px;
  letter-spacing: 0.2px;
  line-height: 24px;
  margin-left: 10px;
}

.noteFormContainer {
  width: 100%;
  padding: 48px;
}
.noteButton {
  max-width: 264px;
}

/*********************************************************
* share component
**********************************************************/
// #05012D {
//   .qrDoneTitle {
//     min-height: 300px;
//     color: $black;
//     font-size: 20px;
//     letter-spacing: 0.2px;
//     line-height: 24px;
//   }
// }

/*********************************************************
* Share History
**********************************************************/

.historyContainer {
  margin: 0 auto;
}

.shareHistoryContainer {
  left: 0% !important;
  margin: 0 auto;
  @media (min-width: 2000px) and (max-width: 3000px) {
    left: 30% !important;
    margin: 0 auto;
  }
  @media (min-width: 1500px) and (max-width: 2000px) {
    left: 15% !important;
    margin: 0 auto;
  }
}

.bottomSpace {
  margin-bottom: 100px;
}

.modalTitle {
  text-align: center;
  color: $black-solid;
  font-family: 'robotoCondensed';
}

.modalPublishTitle {
  text-align: center;
  color: $navy;
  font-size: 20px;
  line-height: 24px;
}

.publishCloseBtn {
  font-family: 'chaney' !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-size: 18px !important;
  line-height: 30px !important;
}

.borderHeader {
  border-bottom: 3px solid;
}
.headerText {
  height: 24px;
  width: 43px;
  color: $black;
  font-family: Roboto;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0.2px;
  line-height: 24px;
}
.tableContainer {
  border: 3px solid;
  margin-top: 40px;
  width: 100%;
}

.trContainer {
  width: 22%;
}

.dataContainer {
  margin: 0px;
  padding: 20px 5px !important;
}
.noteContainer {
  width: 50%;
}
.shareButton {
  color: $royal;
  cursor: pointer;
  padding: 20px !important;
}

.activePage {
  background-color: $navy !important;
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: $white !important;
  border: 1px solid #dee2e6;
}

.shareMainContainer {
  margin: 0 auto;
}

.pageClass {
  background-color: $white;
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: $black;
  border: 1px solid #dee2e6;
}

.headingText {
  color: $black;
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 1.5px;
  line-height: 32px;
  position: relative;
}
.subHeaderText {
  color: $black;
  margin-top: 28px;
  width: 75%;
  font-size: 20px;
  letter-spacing: 0.2px;
  line-height: 24px;
}

.headerTextTh {
  height: 24px;
  width: 43px;
  margin: 0px;
}

/*********************************************************
* New Design
**********************************************************/
.logLineContainer {
  background-color: #05012d;
}

.editBtn {
  background-color: #ecece0;
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  text-transform: uppercase;
  border: 0px;
  padding: 6px 12px;
}

.newSnapBtn {
  background-color: #05012d;
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  text-transform: uppercase;
  border: 0px;
  padding: 8px 28px;
}

.listContainer {
  background-color: #ecece0;
  padding: 20px;
  align-items: center;
  gap: 24px;
  align-self: stretch;
}

.selectedList {
  border: 2px solid var(--navy, #05012d);
}
.disabledList {
  opacity: 0.4;
  background: var(--soft-grey, #ecece0);
}

.radioButton > input[type='radio'] {
  /* remove standard background appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  /* create custom radiobutton appearance */
  display: inline-block;
  width: 16px !important;
  height: 16px !important;
  /* background-color only for content */
  background-clip: content-box;
  border: 2px solid white;
  background-color: #ecece0;
  border-radius: 50%;
  padding: 1px;
}

.radioButton > input[type='radio']:checked {
  background-color: #05012d;
}

.getStartedContainer {
  left: 4%;
  z-index: 1;
}

.gridProjectContainer {
  background-color: $navy;
}

.carousalItems > div > ul > li {
  padding-right: 10px;
}

.gridsnapContainer {
  background-color: #ecece0;
}

.cursor {
  cursor: pointer;
}

.coverImage {
  width: 100%;
  height: 180px;
}

.darkBackground {
  background-color: #1c1c1c;
}

.rectangle {
  height: 7px;
  background-color: #4e4e4e;
  max-width: 80px;
  padding-left: 10px;
  padding-right: 10px;
}

.headingText {
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  line-height: 14px;
  position: relative;
  text-transform: uppercase;
  font-family: 'robotoCondensed';
}

.basicRightContainer {
  background-color: #4e4e4e;
}

.formValue {
  color: #ffffff;
  font-size: 12px;
  letter-spacing: 0.5px;
  line-height: 16px;
}

.fieldlevelDashboard {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  line-height: 16px;
  font-family: 'robotoCondensed';
}

.hr {
  border: 1px solid #ffffff;
  box-sizing: border-box;
}

.tag {
  border-radius: 40px;
  padding: 5px 10px;
  margin: 6px 12px;
  background-color: #4e4e4e;
}

.photoslot {
  position: relative;
  width: 100%;
  padding-bottom: 100%;
  border-radius: 50%;
  background-color: #4e4e4e;
}

.profileImage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  -webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
  filter: grayscale(100%);
}

.profileImageCastMembers {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.pcImage {
  cursor: pointer;
  height: 23px;
  vertical-align: top;
}

.posterImg {
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.imageRow {
  line-height: 0;
  column-count: 2;
  column-gap: 0px;
  margin: 0px auto;
}

.imagebox {
  position: relative;
  height: auto;
  width: 100%;
  padding-right: 0px;
  padding-left: 0px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  float: left;
  margin-bottom: 32px;
}

.youtubeVideo i {
  top: 76px;
  left: 50%;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  @media (max-width: 1400px) {
    top: 126px !important;
  }
  @media (max-width: 1200px) {
    top: 110px !important;
  }
  @media (max-width: 1000px) {
    top: 74px !important;
  }
  @media (max-width: 800px) {
    top: 70px !important;
  }
  @media (max-width: 600px) {
    top: 65px !important;
  }
  @media (max-width: 400px) {
    top: 55px !important;
  }
}

.imageBox {
  max-height: 400px;
  overflow: hidden;
}

.compareImage {
  width: 100%;
  height: 180px !important;
  object-fit: cover;
}

.pcImage {
  cursor: pointer;
  height: 23px;
}

.itemContainer {
  width: 100%;
  height: auto;
  padding-bottom: 36px;
}

.modalTitleText {
  font-size: 20px;
}

.linkText {
  color: $navy;
  opacity: 0.6;
}

.modalBody > div > div {
  background-color: $soft-grey;
  padding: 3rem !important;
}

.modalBody > div > div > div {
  padding: 0px;
}

.snapStepsModalBody > div > div {
  background-color: $soft-grey;
  padding: 0px !important;
  border: none;
}

/* Check box styles. */

/* The container */
.container {
  display: block;
  position: relative;
  padding-left: 24px;
  padding-right: 16px;
  cursor: pointer;
  font-style: normal;
  font-weight: normal;
  font-family: 'maisonNeue';
  font-size: 14px;
  line-height: 16px;
  color: $navy;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
  background-color: $navy;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0px;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: $soft-grey;
  border: 2px solid $navy;
}

/* On mouse-over, add a grey background color */
// .container:hover input ~ .checkmark {
//   background-color: #EC7063 ;
// }

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: $soft-grey;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 5px;
  top: 1px;
  width: 4px;
  height: 9px;
  border: solid $navy;
  border-width: 0 1px 1px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.saveBtn {
  background-color: #05012d;
  font-size: 16px;
  font-family: 'chaney';
  font-weight: normal;
  line-height: 24px;
  word-wrap: break-word;
  align-items: center;
  text-align: center;
  border: 2px solid;
  color: #ffffff;
}

.cancelBtn {
  background-color: #ecece0;
  font-size: 16px;
  font-family: 'chaney';
  font-weight: normal;
  line-height: 24px;
  word-wrap: break-word;
  align-items: center;
  text-align: center;
  border: 2px solid #05012d;
  color: #05012d;
}

//svg icon class
.svgIconClass {
  width: 24px;
  height: 24px;
}

.modalContent > div > div {
  background-color: $soft-grey;
  padding: 3rem !important;
  border: 0px !important;
}

.modalContent > div > div > div {
  padding: 0px;
}

.createButton {
  justify-content: right !important;
  margin-top: 150px;
}
