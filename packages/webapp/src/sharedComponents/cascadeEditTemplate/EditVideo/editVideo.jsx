import React, { useEffect } from 'react';
import { Field } from 'redux-form';
import Button from 'sharedComponents/CascadeTemplate/Common/Button/button';
import RenderField from 'sharedComponents/CascadeTemplate/Common/CascadeRenderField/renderField';
import { required, videoUrlValidation } from 'validation/commonValidation';

// Component for rendering individual video fields

const VideoField = ({
  fields,
  meta: { error, submitFailed },
  onRemoveSubmit,
}) => {
  useEffect(() => {
    if (fields.length === 0) {
      fields.push({
        title: '',
        url: '',
        disc: '',
        type: 'Video',
        videoId: '',
        site: '',
        _id: '',
      });
    }
  }, [fields]);

  const handleAddField = () => {
    fields.push({
      title: '',
      url: '',
      disc: '',
      type: 'Video',
      videoId: '',
      site: '',
      _id: '',
    });
  };

  const handleRemoveField = (index) => {
    const updatedVideos = fields.getAll().filter((_, i) => i !== index);
    fields.remove(index);
    if (onRemoveSubmit) {
      onRemoveSubmit({ videos: updatedVideos }, 'save');
    }
  };

  return (
    <ul className="list-unstyled">
      {fields.map((video, index) => (
        <li key={index} className="mt-4 rounded">
          <div className="row">
            <div className="col-12 p-0">
              <div className="mt-12">
                <Field
                  name={`${video}.title`}
                  component={RenderField}
                  placeholder="Enter title"
                  className="mt-3"
                />
              </div>
              <div className="mt-12">
                <Field
                  name={`${video}.url`}
                  component={RenderField}
                  placeholder="Enter URL"
                  className="mt-3"
                  validate={[required, videoUrlValidation]}
                />
              </div>
              <div className="mt-12">
                <Field
                  name={`${video}.disc`}
                  component={RenderField}
                  placeholder="Enter description"
                  className="mt-3"
                  type="textarea"
                />
              </div>
              <div className="d-flex justify-content-start mt-24">
                <Button
                  btntype="button"
                  size="small"
                  className=" secondary-btn"
                  clickHandler={() => handleRemoveField(index)}
                  buttonValue="Remove"
                />
              </div>
            </div>
          </div>
        </li>
      ))}
      <div className="mt-5">
        <Button
          btntype="button"
          size="xlLarge"
          clickHandler={handleAddField}
          className="secondary-btn"
          buttonValue="Add Video"
        />
      </div>
      {submitFailed && error && <span className="text-danger">{error}</span>}
    </ul>
  );
};

export default VideoField;
