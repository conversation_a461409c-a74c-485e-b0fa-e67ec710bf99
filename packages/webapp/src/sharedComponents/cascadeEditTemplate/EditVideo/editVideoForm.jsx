import React from 'react';
import { FieldArray, reduxForm } from 'redux-form';
import Modal from 'sharedComponents/Modal/modal';
import VideoField from './editVideo';
import EditHeader from '../CommonEdit/editHeader';

// Main form component
const EditVideoForm = ({
  handleSubmit,
  handleVideoSubmit,
  handleCancel,
  t,
  modalContentType,
  setModalContentType,
}) => {
  const modalBody = modalContentType === 'videosInfo' && (
    <p>{t('common:projectCreate.preview.projectVideoDesc')}</p>
  );
  return (
    <>
      <Modal
        modalShow={modalContentType}
        title="Video"
        body={modalBody}
        closeCallback={() => setModalContentType(false)}
        modalSize="lg"
      />
      <form onSubmit={handleSubmit(handleVideoSubmit)}>
        <section className="section video-section">
          <div className="container block">
            <EditHeader
              id="videosSave"
              title="Project Videos"
              showInfoIcon={true}
              handleCancel={handleCancel}
              handleInfoClick={() => setModalContentType('videosInfo')}
            />
            <div className="row mt-8">
              <div className="p-0 col-12">
                <FieldArray
                  name="videos"
                  component={VideoField}
                  onRemoveSubmit={handleVideoSubmit}
                />
              </div>
            </div>
          </div>
        </section>
      </form>
    </>
  );
};

// Export the form component wrapped with reduxForm
export default reduxForm({
  form: 'editVideoForm',
  enableReinitialize: true, // Ensures form reinitializes when initialValues change
})(EditVideoForm);
