import React from 'react';
import ProjectVideo from './projectVideo';
import EditVideoForm from 'sharedComponents/cascadeEditTemplate/EditVideo/editVideoForm';
import { get } from 'lodash';
import mixpanelAnalytics from 'lib/mixpanel';

const Index = ({
  projectPreviewData,
  updateVideos,
  onOffVideoStatus,
  visibleSections,
  editSection,
  setEditSection,
  videosRef,
  setIsClickedOnMenu,
  toggleSectionVisibility,
  t,
  modalContentType,
  setModalContentType,
}) => {
  const videoUrls = get(projectPreviewData, 'videos', []);

  // Extract YouTube ID
  const getIdFromYouTube = (url) => {
    const match = url.match(
      /(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([\w-]+)/,
    );
    if (match) return match[1];
    throw new Error('Invalid YouTube URL');
  };

  // Extract Vimeo ID
  const getIdFromVimeo = (url) => {
    const match = url.match(/vimeo\.com\/(\d+)/);
    if (match) return match[1];
    throw new Error('Invalid Vimeo URL');
  };

  // Get video site and ID
  const getIdFromUrl = async (url) => {
    if (!url) return { id: null, site: null };

    // YouTube check
    const youtubeRegex = /(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)/;
    if (youtubeRegex.test(url)) {
      return { id: getIdFromYouTube(url), site: 'youtube' };
    }

    // Vimeo check
    const vimeoRegex = /vimeo\.com\/(\d+)/;
    if (vimeoRegex.test(url)) {
      return { id: getIdFromVimeo(url), site: 'vimeo' };
    }

    // Unsupported URL
    throw new Error('Unsupported video URL');
  };

  // Handle form submission
  const handleVideoSubmit = async (value) => {
    const video = get(value, 'videos', []);
    const videoData = [];

    for (const vd of video) {
      try {
        const mediaData = await getIdFromUrl(vd.url);
        vd.type = 'Video';
        vd.videoId = mediaData.id;
        vd.site = mediaData.site;
        videoData.push(vd);
      } catch (err) {
        console.error('Invalid video URL:', vd.url, err);
      }
    }

    updateVideos(videoData, projectPreviewData._id);
    setEditSection(false);
    setIsClickedOnMenu(false);
    toggleSectionVisibility(false);

    const newVideoAdded = video.filter((member) => !member._id);
    const existingVideos = video.filter((member) => member._id);

    // Trigger Mixpanel events
    if (newVideoAdded.length) {
      const eventPayloads = newVideoAdded.map((poster) => ({
        posterCount: video.length,
        title: poster.title,
        description: poster.description,
      }));
      mixpanelAnalytics('project_videos_submit', {
        events: eventPayloads,
        pageName: `project_overview_${projectPreviewData._id}`,
      });
    }

    if (existingVideos.length) {
      const summaryPayload = {
        posterCount: video.length,
        pageName: `project_overview_${projectPreviewData._id}`,
      };
      mixpanelAnalytics('project_videos_summary', summaryPayload);
    }
  };

  const handleCancel = () => {
    setEditSection(false);
    setIsClickedOnMenu(false);
    toggleSectionVisibility(false);
  };

  const handleEdit = () => {
    setEditSection('videos');
    setIsClickedOnMenu(true);
    toggleSectionVisibility('videos');
  };

  return (
    <div
      className={`${onOffVideoStatus === 'lock' ? 'sectionHide' : ''} ${
        visibleSections.includes('videos') ? '' : 'shadow-disable'
      }`}
      ref={videosRef}
    >
      {editSection === 'videos' ? (
        <EditVideoForm
          initialValues={{ videos: videoUrls }}
          handleVideoSubmit={handleVideoSubmit}
          handleCancel={handleCancel}
          modalContentType={modalContentType}
          setModalContentType={setModalContentType}
          t={t}
        />
      ) : (
        <ProjectVideo
          videoUrls={videoUrls}
          headingText="Project Video"
          onEdit={handleEdit}
          showEditBtn={true}
        />
      )}
    </div>
  );
};

export default Index;
