const mongoose = require('mongoose');
const { Schema } = mongoose;
const mongoosePaginate = require('mongoose-paginate');
const {
  UserSchema,
  DiscovererSchema,
  CitySchema,
  // SnapshotSchema,
} = require('./common');

// Remove embedded SubmissionSchema and SlateSchema

const CalloutBodySchema = new Schema({
  _id: false,
  companyName: { type: String },
  companyProfileHeading: { type: String },
  companyProfile: { type: String },
  content: { type: String },
  logo: { type: String },
  title: { type: String },
});

const CalloutSchema = new Schema(
  {
    name: { type: String },
    body: CalloutBodySchema,
    genres: { type: String },
    opportunities: { type: String },
    budget: { type: Number, default: 0 },
    city: CitySchema,
    creator: UserSchema,
    discoverer: DiscovererSchema,
    prompt: { type: String },
    submissions: [{ type: Schema.Types.ObjectId, ref: 'Submission' }],
    totalSubmissions: { type: Number, default: 0 },
    slates: [{ type: Schema.Types.ObjectId, ref: 'Submission' }],
    totalSlates: { type: Number, default: 0 },
    deleted: { type: Boolean, default: false },
    isPublished: { type: Boolean, default: false },
    showOrganisationName: { type: Boolean, default: true },
    status: { type: String, enum: ['public', 'private'], default: 'private' },
  },
  { timestamps: true },
);
CalloutSchema.plugin(mongoosePaginate);
const Callout = mongoose.model('Callout', CalloutSchema);
module.exports = Callout;
