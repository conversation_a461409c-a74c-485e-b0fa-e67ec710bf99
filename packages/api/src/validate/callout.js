const Joi = require('joi');

/* callout validation */
module.exports = {
  /* Id validation */
  idSchema: Joi.object({
    id: Joi.string().required(),
    buttonLink: Joi.string().allow(null),
  }),

  params: Joi.object({
    id: Joi.string().required(),
  }),
  /* Email validation */
  emailSchema: Joi.object({
    email: Joi.string().required().trim(),
  }),

  createSchema: Joi.object({
    name: Joi.string().required(),
    body: Joi.object({
      companyName: Joi.string(),
      companyProfile: Joi.string().allow(null),
      content: Joi.string().allow(null),
      logo: Joi.string(),
      title: Joi.string(),
      companyProfileHeading: Joi.string(),
    }),
    genres: Joi.string(),
    prompt: Joi.string(),
    opportunities: Joi.string(),
    budget: Joi.number(),
    city: Joi.object({
      address: Joi.string().required(),
      geoCode: Joi.object(),
    }),
    discovererId: Joi.string(),
  }),

  /* Delete agent schema validation */
  deleteSchema: Joi.object({
    id: Joi.array().required(),
  }),

  updateSchema: Joi.object({
    name: Joi.string(),
    body: Joi.object({
      companyName: Joi.string(),
      companyProfile: Joi.string().allow(null),
      content: Joi.string().allow(null),
      logo: Joi.string(),
      title: Joi.string(),
      companyProfileHeading: Joi.string(),
    }),
    genres: Joi.string(),
    prompt: Joi.string(),
    opportunities: Joi.string(),
    budget: Joi.number(),
    city: Joi.object({
      address: Joi.string().required(),
      geoCode: Joi.object(),
    }),
    discovererId: Joi.string(),
    deleted: Joi.bool().optional().valid(false, true),
    isPublished: Joi.bool(),
    status: Joi.string(),
  }),

  submissionSchema: Joi.object({
    id: Joi.string().required(),
    body: Joi.string().required(),
  }),

  slateSchema: Joi.object({
    id: Joi.string().required(),
    notes: Joi.string().allow(null, ''),
  }),

  submissionAddSchema: Joi.object({
    id: Joi.string().required(),
  }),

  slateStatusSchema: Joi.object({
    id: Joi.string().required(),
    status: Joi.string()
      .required()
      .valid('awaiting_feedback', 'lets_talk', 'tracking', 'not_interested'),
  }),

  submissionStatusSchema: Joi.object({
    id: Joi.string().required(),
    status: Joi.string().required().valid('NEW', 'REJECTED', 'FEEDBACK_SENT'),
  }),

  submissionFeedbackSchema: Joi.object({
    id: Joi.string().required(),
    feedback: Joi.string().required(),
  }),
};
