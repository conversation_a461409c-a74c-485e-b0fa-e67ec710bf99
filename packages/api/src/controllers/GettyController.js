/* eslint-disable no-unused-vars */

const Boom = require('@hapi/boom');
const { get, assign } = require('lodash');
const moment = require('moment');
const GettyToken = require('../models/gettyToken');
const AuthService = require('../services/AuthService');
const UserService = require('../services/UserService');
const GettyService = require('../services/GettyService');

const userService = new UserService();
class GettyController {
  /**
   * Get getty image auth0 token
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async gettyImageAuth0Login(request, h) {
    try {
      const payload = request.payload;
      const loggedUser = request.user;

      /* Get getty image auth0 token */
      const result = await GettyService.getGettyAuth0Token(payload);

      /* Save getty refresh token */
      await GettyToken.findOneAndUpdate(
        { userId: loggedUser._id },
        {
          userId: loggedUser._id,
          refreshToken: result.refresh_token,
        },
        { upsert: true, new: true, safe: true, multi: true },
      );

      /* Delete refresh token from response for security */
      delete result.refresh_token;

      request.logger.info(`Getty image auth0 login successfully`);
      return h
        .response({
          statusCode: 200,
          message: 'Getty image auth0 login successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in GettyController.gettyImageAuth0Login',
        err,
      );
      throw new Boom.Boom(err, { statusCode: 500 });
    }
  }

  /**
   * Getty image signup with google form
   * This function append signup form data in google spreadsheet
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async gettyImageSignUp(request, h) {
    try {
      const payload = request.payload;
      const loggedUser = request.user;

      if (get(loggedUser, 'userMeta.getty.signedUp', false)) {
        return h
          .response({
            statusCode: 409,
            message: 'User already signed up in getty image.',
            error: 'user_already_signed_up',
          })
          .code(409);
      }

      /* Make spreadsheet data for append */
      const spreadSheetData = [];
      spreadSheetData.push(get(payload, 'name', ''));
      spreadSheetData.push(get(payload, 'company', ''));
      spreadSheetData.push(get(payload, 'email', ''));
      spreadSheetData.push(get(payload, 'userName', ''));
      spreadSheetData.push(get(payload, 'location', ''));
      spreadSheetData.push(get(payload, 'projectTitle', ''));
      spreadSheetData.push(get(payload, 'format', ''));
      spreadSheetData.push(get(payload, 'multipleProject', ''));
      spreadSheetData.push(get(payload, 'distributionPlatform', ''));
      spreadSheetData.push(get(payload, 'easyAccessPermission', ''));
      spreadSheetData.push(
        get(payload, 'createdDate', moment().format('YYYY-MM-DD HH:mm:ss')),
      );

      /* Insert getty image form data in google spreadsheet */
      // const result =
      //   await GettyService.insertGettySignupDataInGoogleSheet(spreadSheetData);

      /* Store acknowledge user getty signup in user */
      const m2mToken = await AuthService.authM2MToken();
      const newUserMeta = {
        getty: { signedUp: true },
      };
      const userMeta = assign({}, loggedUser.userMeta, newUserMeta);
      await userService.updateUserMetaIM(loggedUser.email, userMeta, m2mToken);

      request.logger.info(
        `User: ${payload.name} successfully signup in getty image.`,
      );

      return h
        .response({
          statusCode: 200,
          message: `User: ${payload.name} successfully signup in getty image.`,
        })
        .code(200);
    } catch (err) {
      request.logger.error(err, 'error in GettyController.gettyImageSignUp');
      throw new Boom.Boom(err, { statusCode: 500 });
    }
  }

  /**
   * Get auth0 access token when token is expire with the help of refresh token
   * Refresh Token concept implement
   * @param {Hapi request obj} request
   * @param {hapi handler} h
   */
  static async gettyImageAuth0Token(request, h) {
    try {
      const loggedUser = request.user;

      /* Fetch refresh token of this user */
      const token = await GettyToken.findOne(
        { userId: loggedUser._id },
        { refreshToken: 1 },
      );

      /* Insert getty image form data in google spreadsheet */
      const result = await GettyService.getGettyAuth0TokenByRefreshToken(
        token.refreshToken,
      );

      request.logger.info(
        `User: ${loggedUser.email} get getty image auth0 token.`,
      );
      return h
        .response({
          statusCode: 200,
          message: `User: ${loggedUser.email} get getty image auth0 token`,
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        'error in GettyController.gettyImageAuth0Token',
        err,
      );
      throw new Boom.Boom(err, { statusCode: 500 });
    }
  }
}

module.exports = GettyController;
