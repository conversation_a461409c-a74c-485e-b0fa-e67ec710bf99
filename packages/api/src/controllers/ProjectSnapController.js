const { errorHandler } = require('../utils/errorHandler');
const ProjectSnapServices = require('../services/ProjectSnapServices');
const { get, isEmpty } = require('lodash');
const Socket = require('../../config/socket-io');
const projectSnapServices = new ProjectSnapServices();

/**
 * @class Project Snap Controller
 */

class ProjectSnapController {
  /**
   *
   * @param {*} request to get payload and params
   * @param {*} h return type
   * @returns Project snap shot details
   */
  static async updateSnapActivities(request, h) {
    try {
      request.logger.info(
        'ProjectSnapController.updateSnapActivities method called',
      );
      const payload = request.payload;

      const where = {
        _id: payload.projectSnapId, // Match the specific ProjectSnaps document
        'activities.user.email': payload.user.email, // Match the specific activity by its _id
      };
      const query = {
        $set: {
          'activities.$.action': payload.action,
          'activities.$.addedAt': new Date(),
        },
      };
      const findUserSnap = await projectSnapServices.get(where);
      if (!isEmpty(findUserSnap) && payload.action === 'view') {
        return h
          .response({
            statusCode: 200,
            message: 'Project snap update successfully',
            data: findUserSnap,
          })
          .code(200);
      }
      request.logger.info(
        'ProjectSnapController.updateSnapActivities set data is exist',
      );
      let result = await projectSnapServices.update(where, query);
      if (result.modifiedCount === 0) {
        request.logger.info(
          'ProjectSnapController.updateSnapActivities push object if not exist',
        );
        const updatePipeline = {
          $push: {
            activities: { action: payload.action, user: payload.user }, // Push a new activity
          },
        };
        result = await projectSnapServices.update(
          { _id: payload.projectSnapId },
          updatePipeline,
        );
      }

      // Get the updated project snap with populated project data
      const findUserSnapResult = await projectSnapServices.get({
        _id: payload.projectSnapId,
      });

      // Emit socket notification for feedback updates (excluding 'view' actions)
      if (payload.action !== 'view' && process.env.NODE_ENV !== 'testing') {
        const io = Socket.get();
        if (io && findUserSnapResult) {
          // Emit to the project creator's room (using project ID as room)
          const projectId = get(findUserSnapResult, 'projectId');
          if (projectId) {
            const notificationData = {
              data: {
                result: findUserSnapResult,
                isNewNotification: true,
              },
            };
            io.to(projectId.toString()).emit(
              'feedback-notification',
              notificationData,
            );
            request.logger.info(
              `Feedback notification emitted to room: ${projectId} for action: ${payload.action}`,
            );
          }
        }
      }

      return h
        .response({
          statusCode: 200,
          message: 'Project snap update successfully',
          data: findUserSnapResult,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        `ProjectSnapController.updateSnapActivities method error ${err}`,
      );
      return errorHandler(err);
    }
  }

  /**
   *
   * @param {*} request to get payload and params
   * @param {*} h return type
   * @returns Project snap shot details
   */
  static async getProjectSnap(request, h) {
    try {
      request.logger.info('ProjectSnapController.getProjectSnap method called');
      const email = get(request, 'user.email');
      request.logger.info(
        'ProjectSnapController.getProjectSnap set data is exist',
      );
      const where = {
        'activities.user.email': email,
      };
      const result = await projectSnapServices.find(where);
      return h
        .response({
          statusCode: 200,
          message: 'Project snap get successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        `ProjectSnapController.getProjectSnap method error ${err}`,
      );
      return errorHandler(err);
    }
  }
}

module.exports = ProjectSnapController;
